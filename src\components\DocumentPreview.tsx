import type { DocumentType, FormData } from '../types'
import StudentIDTemplate from './templates/StudentIDTemplate'
import UniversityTranscriptTemplate from './templates/UniversityTranscriptTemplate'
import HighSchoolTranscriptTemplate from './templates/HighSchoolTranscriptTemplate'
import TuitionReceiptTemplate from './templates/TuitionReceiptTemplate'
import ClassScheduleTemplate from './templates/ClassScheduleTemplate'

interface DocumentPreviewProps {
  selectedDocuments: DocumentType[]
  formData: FormData
  onBack: () => void
  onEdit: () => void
}

export default function DocumentPreview({ 
  selectedDocuments, 
  formData, 
  onBack, 
  onEdit 
}: DocumentPreviewProps) {
  
  const generateHTML = (documentType: DocumentType) => {
    // Generate a complete HTML document for download
    const templateContent = getTemplateHTML(documentType)
    if (!templateContent) return ''

    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${getDocumentTitle(documentType)} - ${formData.fullName}</title>
  <style>
    ${getDocumentStyles()}
  </style>
</head>
<body>
  ${templateContent}
</body>
</html>`
  }

  const getDocumentTitle = (documentType: DocumentType) => {
    switch (documentType) {
      case 'studentId': return 'Student ID Card'
      case 'universityTranscript': return 'University Transcript'
      case 'highSchoolTranscript': return 'High School Transcript'
      case 'tuitionReceipt': return 'Tuition Receipt'
      case 'classSchedule': return 'Class Schedule'
      default: return 'Academic Document'
    }
  }

  const getTemplateHTML = (documentType: DocumentType) => {
    // This would need to be implemented to convert React components to HTML strings
    // For now, return a basic HTML structure
    const title = getDocumentTitle(documentType)
    return `<div style="max-width: 800px; margin: 0 auto; padding: 20px; font-family: 'Times New Roman', serif;">
      <h1 style="text-align: center; color: #A31F34; margin-bottom: 20px;">${title}</h1>
      <div style="background: white; padding: 20px; border: 1px solid #ccc; border-radius: 8px;">
        <p><strong>Student Name:</strong> ${formData.fullName || 'Not provided'}</p>
        <p><strong>Student ID:</strong> ${formData.studentId || 'Not provided'}</p>
        <p><strong>Major:</strong> ${formData.major || 'Not provided'}</p>
        <p><strong>School:</strong> ${formData.school || 'Not provided'}</p>
        <p style="margin-top: 20px; text-align: center; font-size: 12px; color: #666;">
          Generated on ${new Date().toLocaleDateString()}
        </p>
      </div>
    </div>`
  }

  const getDocumentStyles = () => {
    return `
      body {
        margin: 0;
        padding: 20px;
        background: #f5f5f5;
        font-family: 'Times New Roman', serif;
      }
      @media print {
        body { background: white; padding: 0; }
        .no-print { display: none !important; }
      }
    `
  }

  const downloadDocument = (documentType: DocumentType) => {
    const html = generateHTML(documentType)
    const blob = new Blob([html], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${documentType}-${formData.fullName.replace(/\s+/g, '-').toLowerCase()}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const downloadAllDocuments = () => {
    selectedDocuments.forEach(docType => {
      setTimeout(() => downloadDocument(docType), 100)
    })
  }

  const printDocument = (documentType: DocumentType) => {
    const html = generateHTML(documentType)
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(html)
      printWindow.document.close()
      printWindow.focus()
      setTimeout(() => {
        printWindow.print()
        printWindow.close()
      }, 250)
    }
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Document Preview
        </h2>
        <p className="text-lg text-gray-600 mb-6">
          Review your documents before downloading. You can edit the information or download the documents.
        </p>
        
        <div className="flex flex-wrap gap-4">
          <button onClick={onBack} className="btn-secondary">
            Back to Form
          </button>
          <button onClick={onEdit} className="btn-secondary">
            Edit Information
          </button>
          <button onClick={downloadAllDocuments} className="btn-primary">
            Download All Documents
          </button>
        </div>
      </div>

      <div className="space-y-8">
        {selectedDocuments.map((documentType) => (
          <div key={documentType} className="card">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                {documentType.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </h3>
              <div className="flex gap-2">
                <button
                  onClick={() => printDocument(documentType)}
                  className="btn-secondary text-sm"
                >
                  Print
                </button>
                <button
                  onClick={() => downloadDocument(documentType)}
                  className="btn-primary text-sm"
                >
                  Download HTML
                </button>
              </div>
            </div>
            
            <div className="border border-gray-200 rounded-lg overflow-hidden">
              <div className="bg-gray-50 p-4 border-b border-gray-200">
                <p className="text-sm text-gray-600">
                  Preview - This is how your document will look when printed or exported
                </p>
              </div>
              
              <div className="p-6 bg-white" style={{ minHeight: '400px' }}>
                {documentType === 'studentId' && (
                  <StudentIDTemplate formData={formData} />
                )}
                {documentType === 'universityTranscript' && (
                  <UniversityTranscriptTemplate formData={formData} />
                )}
                {documentType === 'highSchoolTranscript' && (
                  <HighSchoolTranscriptTemplate formData={formData} />
                )}
                {documentType === 'tuitionReceipt' && (
                  <TuitionReceiptTemplate formData={formData} />
                )}
                {documentType === 'classSchedule' && (
                  <ClassScheduleTemplate formData={formData} />
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 text-center">
        <div className="card bg-blue-50 border-blue-200">
          <h4 className="font-medium text-blue-900 mb-2">💡 Tips for Best Results</h4>
          <ul className="text-sm text-blue-800 text-left space-y-1">
            <li>• Use the Print option for immediate printing with proper formatting</li>
            <li>• Download HTML files can be opened in any browser and converted to PDF</li>
            <li>• For PDF conversion, use your browser's "Print to PDF" feature</li>
            <li>• Ensure all information is correct before downloading</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
