import type { FormData } from '../../types'

interface ClassScheduleTemplateProps {
  formData: FormData
}

export default function ClassScheduleTemplate({ formData }: ClassScheduleTemplateProps) {
  const currentDate = new Date().toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })

  // Sample schedule if none provided
  const sampleSchedule = [
    { courseId: 'CSE 110', courseName: 'Principles of Programming', instructor: 'Dr<PERSON> <PERSON>', days: 'MWF', time: '9:00 AM - 9:50 AM', location: 'BYENG 210', credits: '3', section: '001' },
    { courseId: 'MAT 265', courseName: 'Calculus for Engineers I', instructor: 'Prof<PERSON>', days: 'TTh', time: '10:30 AM - 11:45 AM', location: 'PSA 101', credits: '4', section: '002' },
    { courseId: 'ENG 101', courseName: 'First-Year Composition', instructor: 'Dr<PERSON> <PERSON>', days: 'MWF', time: '1:30 PM - 2:20 PM', location: 'LL 220', credits: '3', section: '015' },
    { courseId: 'PHY 121', courseName: 'University Physics I', instructor: 'Dr<PERSON>', days: 'TTh', time: '2:00 PM - 3:15 PM', location: 'PSF 173', credits: '4', section: '003' },
    { courseId: 'PHY 122', courseName: 'Physics Lab I', instructor: 'TA Davis', days: 'W', time: '3:00 PM - 5:50 PM', location: 'PSF 174', credits: '1', section: '010' },
  ]

  const schedule = formData.scheduleCourses?.length > 0 ? formData.scheduleCourses : sampleSchedule
  const totalCredits = schedule.reduce((sum, course) => sum + parseInt(course.credits || '0'), 0)

  return (
    <div className="max-w-5xl mx-auto bg-white p-8 font-serif text-sm">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="bg-academic-maroon text-white p-6 rounded-lg">
          <div className="flex items-center justify-center space-x-4 mb-3">
            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center">
              <span className="text-academic-maroon font-bold text-lg">ASU</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold">ARIZONA STATE UNIVERSITY</h1>
              <p className="text-lg">Office of the Registrar</p>
            </div>
          </div>
          <h2 className="text-xl font-bold bg-academic-gold text-academic-maroon py-2 px-4 rounded">
            CLASS SCHEDULE
          </h2>
        </div>
      </div>

      {/* Student and Term Information */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-bold text-academic-maroon mb-3">STUDENT INFORMATION</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-semibold">Name:</span>
                <span>{formData.fullName || 'Student Name'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Student ID:</span>
                <span>{formData.studentId || '1234567890'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Major:</span>
                <span>{formData.major || 'Computer Science'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">School:</span>
                <span>{formData.school || 'Engineering'}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-bold text-academic-maroon mb-3">TERM INFORMATION</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-semibold">Term:</span>
                <span>{formData.scheduleTerms || 'Fall 2024'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Total Credits:</span>
                <span className="font-bold">{totalCredits}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Enrollment Status:</span>
                <span>{totalCredits >= 12 ? 'Full-Time' : 'Part-Time'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Schedule Generated:</span>
                <span>{currentDate}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Course Schedule Table */}
      <div className="mb-8">
        <h3 className="bg-academic-maroon text-white p-3 font-bold">ENROLLED COURSES</h3>
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-3 text-left">Course</th>
              <th className="border border-gray-300 p-3 text-left">Course Title</th>
              <th className="border border-gray-300 p-3 text-left">Section</th>
              <th className="border border-gray-300 p-3 text-left">Credits</th>
              <th className="border border-gray-300 p-3 text-left">Days</th>
              <th className="border border-gray-300 p-3 text-left">Time</th>
              <th className="border border-gray-300 p-3 text-left">Location</th>
              <th className="border border-gray-300 p-3 text-left">Instructor</th>
            </tr>
          </thead>
          <tbody>
            {schedule.map((course, index) => (
              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                <td className="border border-gray-300 p-3 font-semibold">{course.courseId}</td>
                <td className="border border-gray-300 p-3">{course.courseName}</td>
                <td className="border border-gray-300 p-3">{course.section}</td>
                <td className="border border-gray-300 p-3 text-center">{course.credits}</td>
                <td className="border border-gray-300 p-3 text-center">{course.days}</td>
                <td className="border border-gray-300 p-3">{course.time}</td>
                <td className="border border-gray-300 p-3">{course.location}</td>
                <td className="border border-gray-300 p-3">{course.instructor}</td>
              </tr>
            ))}
            <tr className="bg-academic-gold">
              <td className="border border-gray-300 p-3 font-bold" colSpan={3}>TOTAL CREDITS</td>
              <td className="border border-gray-300 p-3 text-center font-bold">{totalCredits}</td>
              <td className="border border-gray-300 p-3" colSpan={4}></td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Weekly Schedule Grid */}
      <div className="mb-8">
        <h3 className="bg-academic-maroon text-white p-3 font-bold">WEEKLY SCHEDULE OVERVIEW</h3>
        <div className="border border-gray-300">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 p-2 w-20">Time</th>
                <th className="border border-gray-300 p-2">Monday</th>
                <th className="border border-gray-300 p-2">Tuesday</th>
                <th className="border border-gray-300 p-2">Wednesday</th>
                <th className="border border-gray-300 p-2">Thursday</th>
                <th className="border border-gray-300 p-2">Friday</th>
              </tr>
            </thead>
            <tbody>
              {['8:00 AM', '9:00 AM', '10:00 AM', '11:00 AM', '12:00 PM', '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM', '5:00 PM'].map((time, index) => (
                <tr key={index}>
                  <td className="border border-gray-300 p-2 bg-gray-50 font-semibold text-xs">{time}</td>
                  {['M', 'T', 'W', 'Th', 'F'].map((day) => {
                    const courseForSlot = schedule.find(course => {
                      const courseDays = course.days.replace('Th', 'R') // Handle Thursday
                      const courseTime = course.time.split(' - ')[0]
                      return courseDays.includes(day === 'Th' ? 'R' : day) && courseTime.includes(time.split(':')[0])
                    })
                    
                    return (
                      <td key={day} className="border border-gray-300 p-1 h-12 text-xs">
                        {courseForSlot && (
                          <div className="bg-academic-maroon text-white p-1 rounded text-center">
                            <div className="font-semibold">{courseForSlot.courseId}</div>
                            <div className="text-xs">{courseForSlot.location}</div>
                          </div>
                        )}
                      </td>
                    )
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Important Dates and Information */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-bold text-blue-900 mb-3">📅 Important Dates</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Classes Begin: August 22, 2024</li>
            <li>• Last Day to Add: August 29, 2024</li>
            <li>• Last Day to Drop: September 5, 2024</li>
            <li>• Midterm Week: October 14-18, 2024</li>
            <li>• Last Day to Withdraw: November 1, 2024</li>
            <li>• Final Exams: December 9-13, 2024</li>
          </ul>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-bold text-yellow-900 mb-3">⚠️ Important Notes</h4>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>• Verify room locations before first class</li>
            <li>• Check ASU email for course updates</li>
            <li>• Purchase textbooks through ASU Bookstore</li>
            <li>• Set up Canvas access for online materials</li>
            <li>• Contact instructors for any questions</li>
          </ul>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-xs text-gray-600 border-t pt-6">
        <div className="mb-4">
          <p className="font-bold">ARIZONA STATE UNIVERSITY - OFFICE OF THE REGISTRAR</p>
          <p>PO Box 870112, Tempe, AZ 85287-0112</p>
          <p>Phone: (************* | Email: <EMAIL> | Web: students.asu.edu</p>
        </div>
        <div className="flex justify-between items-center">
          <span>Schedule Generated: {currentDate}</span>
          <span>Term: {formData.scheduleTerms || 'Fall 2024'}</span>
          <span>Page 1 of 1</span>
        </div>
      </div>
    </div>
  )
}
