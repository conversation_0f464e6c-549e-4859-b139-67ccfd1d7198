import type { Course } from '../types'

interface CourseEditorProps {
  courses: Course[]
  onChange: (courses: Course[]) => void
  title: string
}

export default function CourseEditor({ courses, onChange, title }: CourseEditorProps) {
  const addCourse = () => {
    const newCourse: Course = {
      moYear: '',
      id: '',
      title: '',
      grade: '',
      creditsAttempted: '',
      creditsEarned: '',
      qualityPoints: '',
      rep: ''
    }
    onChange([...courses, newCourse])
  }

  const updateCourse = (index: number, field: keyof Course, value: string) => {
    const updatedCourses = courses.map((course, i) => 
      i === index ? { ...course, [field]: value } : course
    )
    onChange(updatedCourses)
  }

  const removeCourse = (index: number) => {
    onChange(courses.filter((_, i) => i !== index))
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h4 className="text-lg font-medium text-gray-900">{title}</h4>
        <button
          onClick={addCourse}
          className="btn-secondary text-sm"
        >
          Add Course
        </button>
      </div>

      {courses.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No courses added yet. Click "Add Course" to get started.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {courses.map((course, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <div className="flex justify-between items-center mb-4">
                <h5 className="font-medium text-gray-900">Course {index + 1}</h5>
                <button
                  onClick={() => removeCourse(index)}
                  className="text-red-600 hover:text-red-800 text-sm"
                >
                  Remove
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="form-label">Month/Year</label>
                  <input
                    type="text"
                    value={course.moYear}
                    onChange={(e) => updateCourse(index, 'moYear', e.target.value)}
                    placeholder="08/2020"
                    className="form-input"
                  />
                </div>
                
                <div>
                  <label className="form-label">Course ID</label>
                  <input
                    type="text"
                    value={course.id}
                    onChange={(e) => updateCourse(index, 'id', e.target.value)}
                    placeholder="CSE 110"
                    className="form-input"
                  />
                </div>
                
                <div className="md:col-span-2">
                  <label className="form-label">Course Title</label>
                  <input
                    type="text"
                    value={course.title}
                    onChange={(e) => updateCourse(index, 'title', e.target.value)}
                    placeholder="Principles of Programming"
                    className="form-input"
                  />
                </div>
                
                <div>
                  <label className="form-label">Grade</label>
                  <select
                    value={course.grade}
                    onChange={(e) => updateCourse(index, 'grade', e.target.value)}
                    className="form-input"
                  >
                    <option value="">Select Grade</option>
                    <option value="A">A</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B">B</option>
                    <option value="B-">B-</option>
                    <option value="C+">C+</option>
                    <option value="C">C</option>
                    <option value="C-">C-</option>
                    <option value="D+">D+</option>
                    <option value="D">D</option>
                    <option value="F">F</option>
                    <option value="P">P (Pass)</option>
                    <option value="NP">NP (No Pass)</option>
                    <option value="W">W (Withdraw)</option>
                  </select>
                </div>
                
                <div>
                  <label className="form-label">Credits Attempted</label>
                  <input
                    type="number"
                    step="0.1"
                    value={course.creditsAttempted}
                    onChange={(e) => updateCourse(index, 'creditsAttempted', e.target.value)}
                    placeholder="3.0"
                    className="form-input"
                  />
                </div>
                
                <div>
                  <label className="form-label">Credits Earned</label>
                  <input
                    type="number"
                    step="0.1"
                    value={course.creditsEarned}
                    onChange={(e) => updateCourse(index, 'creditsEarned', e.target.value)}
                    placeholder="3.0"
                    className="form-input"
                  />
                </div>
                
                <div>
                  <label className="form-label">Quality Points</label>
                  <input
                    type="number"
                    step="0.1"
                    value={course.qualityPoints}
                    onChange={(e) => updateCourse(index, 'qualityPoints', e.target.value)}
                    placeholder="1.0"
                    className="form-input"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
