import type { FormData } from '../../types'

interface HighSchoolTranscriptTemplateProps {
  formData: FormData
}

export default function HighSchoolTranscriptTemplate({ formData }: HighSchoolTranscriptTemplateProps) {
  const currentDate = new Date().toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })

  // Sample high school courses if none provided
  const sampleCourses = [
    { year: '2020-2021', semester: 'Fall', courseId: 'ENG101', courseName: 'English I', grade: 'A', credits: '1.0', level: 'Regular' },
    { year: '2020-2021', semester: 'Fall', courseId: 'ALG101', courseName: 'Algebra I', grade: 'B+', credits: '1.0', level: 'Regular' },
    { year: '2020-2021', semester: 'Fall', courseId: 'BIO101', courseName: 'Biology', grade: 'A-', credits: '1.0', level: 'Regular' },
    { year: '2020-2021', semester: 'Spring', courseId: 'ENG102', courseName: 'English II', grade: 'A', credits: '1.0', level: 'Honors' },
    { year: '2021-2022', semester: 'Fall', courseId: 'CHEM101', courseName: 'Chemistry', grade: 'B+', credits: '1.0', level: 'AP' },
  ]

  const courses = formData.highSchoolCourses?.length > 0 ? formData.highSchoolCourses : sampleCourses

  return (
    <div className="max-w-4xl mx-auto bg-white p-8 font-serif text-sm">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="bg-academic-navy text-white p-4 rounded-t-lg">
          <h1 className="text-2xl font-bold mb-2">{formData.highSchoolName || 'PHOENIX HIGH SCHOOL'}</h1>
          <p className="text-sm">Official Academic Transcript</p>
        </div>
        <div className="bg-gray-100 p-3 rounded-b-lg border">
          <div className="flex justify-between text-xs">
            <span>1234 Education Blvd, Phoenix, AZ 85001</span>
            <span>Phone: (*************</span>
            <span>Fax: (*************</span>
          </div>
        </div>
      </div>

      {/* Document Info */}
      <div className="flex justify-between text-xs mb-6 p-3 bg-gray-50 rounded">
        <span>Print Date: {currentDate}</span>
        <span>Document ID: PHS-TR-{Date.now()}</span>
        <span>Transcript Request Date: {currentDate}</span>
      </div>

      {/* Student Information */}
      <div className="mb-6">
        <h3 className="bg-academic-navy text-white p-2 font-bold">STUDENT INFORMATION</h3>
        <table className="w-full border-collapse border border-gray-300">
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50 w-1/4">Student Name</td>
              <td className="border border-gray-300 p-2 w-1/4">{formData.fullName || 'Student Name'}</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50 w-1/4">Student ID</td>
              <td className="border border-gray-300 p-2 w-1/4">{formData.studentId || '2024001'}</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Date of Birth</td>
              <td className="border border-gray-300 p-2">{formData.dateOfBirth || 'June 15, 2005'}</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Graduation Date</td>
              <td className="border border-gray-300 p-2">{formData.graduationDate || 'May 25, 2024'}</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Address</td>
              <td className="border border-gray-300 p-2 colspan-3" colSpan={3}>{formData.address || '123 Student Lane, Phoenix, AZ 85001'}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Academic Summary */}
      <div className="mb-6">
        <h3 className="bg-academic-navy text-white p-2 font-bold">ACADEMIC SUMMARY</h3>
        <table className="w-full border-collapse border border-gray-300">
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50 w-1/4">Cumulative GPA</td>
              <td className="border border-gray-300 p-2 w-1/4">{formData.highSchoolGpa || '3.75'} (4.0 scale)</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50 w-1/4">Class Rank</td>
              <td className="border border-gray-300 p-2 w-1/4">45 of 312</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Total Credits</td>
              <td className="border border-gray-300 p-2">24.0</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Diploma Type</td>
              <td className="border border-gray-300 p-2">Standard High School Diploma</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Honors Courses</td>
              <td className="border border-gray-300 p-2">6</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">AP Courses</td>
              <td className="border border-gray-300 p-2">4</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Course History */}
      <div className="mb-6">
        <h3 className="bg-academic-navy text-white p-2 font-bold">COURSE HISTORY</h3>
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-2 text-left">Year</th>
              <th className="border border-gray-300 p-2 text-left">Semester</th>
              <th className="border border-gray-300 p-2 text-left">Course ID</th>
              <th className="border border-gray-300 p-2 text-left">Course Name</th>
              <th className="border border-gray-300 p-2 text-left">Level</th>
              <th className="border border-gray-300 p-2 text-left">Grade</th>
              <th className="border border-gray-300 p-2 text-left">Credits</th>
            </tr>
          </thead>
          <tbody>
            {courses.map((course, index) => (
              <tr key={index}>
                <td className="border border-gray-300 p-2">{course.year}</td>
                <td className="border border-gray-300 p-2">{course.semester}</td>
                <td className="border border-gray-300 p-2">{course.courseId}</td>
                <td className="border border-gray-300 p-2">{course.courseName}</td>
                <td className="border border-gray-300 p-2">{course.level}</td>
                <td className="border border-gray-300 p-2 font-semibold">{course.grade}</td>
                <td className="border border-gray-300 p-2">{course.credits}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Graduation Requirements */}
      <div className="mb-6">
        <h3 className="bg-academic-navy text-white p-2 font-bold">GRADUATION REQUIREMENTS</h3>
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-2 text-left">Subject Area</th>
              <th className="border border-gray-300 p-2 text-left">Required</th>
              <th className="border border-gray-300 p-2 text-left">Completed</th>
              <th className="border border-gray-300 p-2 text-left">Status</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">English</td>
              <td className="border border-gray-300 p-2">4.0</td>
              <td className="border border-gray-300 p-2">4.0</td>
              <td className="border border-gray-300 p-2 text-green-600 font-semibold">✓ Complete</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">Mathematics</td>
              <td className="border border-gray-300 p-2">4.0</td>
              <td className="border border-gray-300 p-2">4.0</td>
              <td className="border border-gray-300 p-2 text-green-600 font-semibold">✓ Complete</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">Science</td>
              <td className="border border-gray-300 p-2">3.0</td>
              <td className="border border-gray-300 p-2">4.0</td>
              <td className="border border-gray-300 p-2 text-green-600 font-semibold">✓ Complete</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">Social Studies</td>
              <td className="border border-gray-300 p-2">3.0</td>
              <td className="border border-gray-300 p-2">3.0</td>
              <td className="border border-gray-300 p-2 text-green-600 font-semibold">✓ Complete</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2">Electives</td>
              <td className="border border-gray-300 p-2">10.0</td>
              <td className="border border-gray-300 p-2">9.0</td>
              <td className="border border-gray-300 p-2 text-green-600 font-semibold">✓ Complete</td>
            </tr>
            <tr className="bg-gray-50 font-semibold">
              <td className="border border-gray-300 p-2">TOTAL</td>
              <td className="border border-gray-300 p-2">24.0</td>
              <td className="border border-gray-300 p-2">24.0</td>
              <td className="border border-gray-300 p-2 text-green-600">✓ REQUIREMENTS MET</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Official Seal and Signatures */}
      <div className="flex justify-between mt-12 mb-6">
        <div className="text-center">
          <div className="w-24 h-24 border-2 border-gray-400 rounded-full mx-auto mb-2 flex items-center justify-center text-xs text-gray-500">
            OFFICIAL<br/>SEAL
          </div>
          <p className="text-xs font-semibold">School Seal</p>
        </div>
        <div className="text-center">
          <div className="w-48 h-16 border-b border-gray-400 mb-2"></div>
          <p className="text-xs">Principal</p>
          <p className="text-xs font-semibold">{formData.highSchoolName || 'Phoenix High School'}</p>
        </div>
        <div className="text-center">
          <div className="w-48 h-16 border-b border-gray-400 mb-2"></div>
          <p className="text-xs">Registrar</p>
          <p className="text-xs">Date: {currentDate}</p>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-xs text-gray-600 border-t pt-4">
        <p className="font-bold">THIS IS AN OFFICIAL TRANSCRIPT ISSUED BY {(formData.highSchoolName || 'PHOENIX HIGH SCHOOL').toUpperCase()}</p>
        <p>This transcript contains a complete record of this student's academic work.</p>
        <p>Any alteration of this document renders it invalid.</p>
      </div>
    </div>
  )
}
