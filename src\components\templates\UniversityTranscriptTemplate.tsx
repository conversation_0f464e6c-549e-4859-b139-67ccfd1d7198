import type { FormData } from '../../types'

interface UniversityTranscriptTemplateProps {
  formData: FormData
}

export default function UniversityTranscriptTemplate({ formData }: UniversityTranscriptTemplateProps) {
  const currentDate = new Date().toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })

  return (
    <div className="max-w-4xl mx-auto bg-white p-8 font-serif text-sm">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="bg-academic-maroon text-white p-4 rounded-t-lg">
          <div className="flex items-center justify-center space-x-3 mb-2">
            <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
              <span className="text-academic-maroon font-bold">ASU</span>
            </div>
            <h1 className="text-2xl font-bold">ARIZONA STATE UNIVERSITY</h1>
          </div>
        </div>
        <div className="bg-gray-100 p-3 rounded-b-lg border">
          <h2 className="text-xl font-bold text-academic-maroon">OFFICIAL ACADEMIC TRANSCRIPT</h2>
        </div>
      </div>

      {/* Document Info */}
      <div className="flex justify-between text-xs mb-6 p-3 bg-gray-50 rounded">
        <span>Print Date: {currentDate}</span>
        <span>Document ID: ASU-TR-{Date.now()}</span>
        <span>Verification Code: {formData.verificationCodeTr || 'ASU2024-TR-001'}</span>
      </div>

      {/* Student Information */}
      <div className="mb-6">
        <h3 className="bg-academic-maroon text-white p-2 font-bold">STUDENT INFORMATION</h3>
        <table className="w-full border-collapse border border-gray-300">
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50 w-1/4">Full Name</td>
              <td className="border border-gray-300 p-2 w-1/4">{formData.fullName || 'Student Name'}</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50 w-1/4">Gender</td>
              <td className="border border-gray-300 p-2 w-1/4">{formData.gender || 'Not Specified'}</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Student ID</td>
              <td className="border border-gray-300 p-2">{formData.studentId || '1234567890'}</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Date of Birth</td>
              <td className="border border-gray-300 p-2">{formData.dateOfBirth || 'Not Provided'}</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Major</td>
              <td className="border border-gray-300 p-2 colspan-3" colSpan={3}>{formData.major || 'Computer Science'}</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Address</td>
              <td className="border border-gray-300 p-2 colspan-3" colSpan={3}>{formData.address || 'Address Not Provided'}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Academic Summary */}
      <div className="mb-6">
        <h3 className="bg-academic-maroon text-white p-2 font-bold">ACADEMIC SUMMARY</h3>
        <table className="w-full border-collapse border border-gray-300">
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50 w-1/4">Institutional GPA</td>
              <td className="border border-gray-300 p-2 w-1/4">{formData.institutionalGpa || '3.75'}</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50 w-1/4">Total Credits Attempted</td>
              <td className="border border-gray-300 p-2 w-1/4">{formData.totalCreditsAttempted || '120.0'}</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Major GPA</td>
              <td className="border border-gray-300 p-2">{formData.majorGpa || '3.85'}</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Total Credits Earned</td>
              <td className="border border-gray-300 p-2">{formData.totalCreditsEarned || '120.0'}</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Academic Standing</td>
              <td className="border border-gray-300 p-2">{formData.academicStanding || 'Good Standing'}</td>
              <td className="border border-gray-300 p-2 font-semibold bg-gray-50">Total Quality Points</td>
              <td className="border border-gray-300 p-2">{formData.totalQualityPoints || '450.0'}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Course History */}
      {formData.courses && formData.courses.length > 0 && (
        <div className="mb-6">
          <h3 className="bg-academic-maroon text-white p-2 font-bold">COURSE HISTORY</h3>
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 p-2 text-left">Term</th>
                <th className="border border-gray-300 p-2 text-left">Course ID</th>
                <th className="border border-gray-300 p-2 text-left">Course Title</th>
                <th className="border border-gray-300 p-2 text-left">Grade</th>
                <th className="border border-gray-300 p-2 text-left">Credits</th>
                <th className="border border-gray-300 p-2 text-left">Quality Points</th>
              </tr>
            </thead>
            <tbody>
              {formData.courses.map((course, index) => (
                <tr key={index}>
                  <td className="border border-gray-300 p-2">{course.moYear}</td>
                  <td className="border border-gray-300 p-2">{course.id}</td>
                  <td className="border border-gray-300 p-2">{course.title}</td>
                  <td className="border border-gray-300 p-2 font-semibold">{course.grade}</td>
                  <td className="border border-gray-300 p-2">{course.creditsEarned}</td>
                  <td className="border border-gray-300 p-2">{course.qualityPoints}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Prior Education */}
      <div className="mb-6">
        <h3 className="bg-academic-maroon text-white p-2 font-bold">PRIOR SCHOOLS ATTENDED</h3>
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-2 text-left">Institution</th>
              <th className="border border-gray-300 p-2 text-left">Credits</th>
              <th className="border border-gray-300 p-2 text-left">Degrees</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-2">{formData.priorSchool || 'High School'}</td>
              <td className="border border-gray-300 p-2">{formData.priorSchoolCredits || 'N/A'}</td>
              <td className="border border-gray-300 p-2">{formData.priorSchoolDegree || 'High School Diploma'}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Signatures */}
      <div className="flex justify-between mt-12 mb-6">
        <div className="text-center">
          <div className="w-48 h-16 border-b border-gray-400 mb-2"></div>
          <p className="text-xs">University Registrar</p>
        </div>
        <div className="text-center">
          <div className="w-48 h-16 border-b border-gray-400 mb-2"></div>
          <p className="text-xs">Dean of Academic Affairs</p>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-xs text-gray-600 border-t pt-4">
        <p className="font-bold">THIS IS AN OFFICIAL ACADEMIC TRANSCRIPT ISSUED BY ARIZONA STATE UNIVERSITY.</p>
        <p>Document ID: ASU-TR-{Date.now()} • Generated: {currentDate}</p>
        <p>To verify the authenticity of this document, please visit verify.asu.edu</p>
      </div>

      {/* Watermark */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-5">
        <div className="text-9xl font-bold text-gray-400 transform rotate-45">
          OFFICIAL
        </div>
      </div>
    </div>
  )
}
