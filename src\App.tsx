import { useState } from 'react'
import DocumentSelector from './components/DocumentSelector'
import DocumentForm from './components/DocumentForm'
import DocumentPreview from './components/DocumentPreview'
import type { DocumentType, FormData } from './types'

function App() {
  const [currentStep, setCurrentStep] = useState<'select' | 'form' | 'preview'>('select')
  const [selectedDocuments, setSelectedDocuments] = useState<DocumentType[]>([])
  const [formData, setFormData] = useState<FormData>({
    // Common fields
    fullName: '',
    studentId: '',
    major: '',
    school: '',

    // Student ID specific
    validThrough: '',

    // Student Info specific
    campus: '',
    expectedStart: '',
    expectedCompletion: '',
    issueDate: '',
    validUntil: '',
    verificationCode: '',

    // Transcript specific
    gender: '',
    dateOfBirth: '',
    age: '',
    ethnicity: '',
    address: '',
    verificationCodeTr: '',
    institutionalGpa: '',
    majorGpa: '',
    academicStanding: '',
    totalCreditsAttempted: '',
    totalCreditsEarned: '',
    totalQualityPoints: '',
    priorSchool: '',
    priorSchoolCredits: '',
    priorSchoolDegree: '',
    courses: [],

    // High School Transcript specific
    highSchoolName: '',
    graduationDate: '',
    highSchoolGpa: '',
    highSchoolCourses: [],

    // Tuition Receipt specific
    semester: '',
    academicYear: '',
    tuitionAmount: '',
    feesAmount: '',
    totalAmount: '',
    paymentDate: '',
    paymentMethod: '',

    // Class Schedule specific
    scheduleTerms: '',
    scheduleCourses: []
  })

  const handleDocumentSelection = (documents: DocumentType[]) => {
    setSelectedDocuments(documents)
    setCurrentStep('form')
  }

  const handleFormSubmit = (data: FormData) => {
    setFormData(data)
    setCurrentStep('preview')
  }

  const handleBackToSelection = () => {
    setCurrentStep('select')
    setSelectedDocuments([])
  }

  const handleBackToForm = () => {
    setCurrentStep('form')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <h1 className="text-2xl font-bold text-academic-maroon">
              Academic Document Generator
            </h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Step {currentStep === 'select' ? '1' : currentStep === 'form' ? '2' : '3'} of 3
              </span>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentStep === 'select' && (
          <DocumentSelector
            onDocumentSelection={handleDocumentSelection}
          />
        )}

        {currentStep === 'form' && (
          <DocumentForm
            selectedDocuments={selectedDocuments}
            initialData={formData}
            onSubmit={handleFormSubmit}
            onBack={handleBackToSelection}
          />
        )}

        {currentStep === 'preview' && (
          <DocumentPreview
            selectedDocuments={selectedDocuments}
            formData={formData}
            onBack={handleBackToForm}
            onEdit={() => setCurrentStep('form')}
          />
        )}
      </main>
    </div>
  )
}

export default App
