/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        academic: {
          maroon: '#A31F34',
          gold: '#FFC107',
          navy: '#1e3a8a',
          gray: '#6b7280',
        }
      },
      fontFamily: {
        'academic': ['"Times New Roman"', 'serif'],
        'modern': ['Inter', 'system-ui', 'sans-serif'],
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'serif': ['"Times New Roman"', 'serif'],
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      screens: {
        'print': {'raw': 'print'},
      }
    },
  },
  plugins: [],
}
