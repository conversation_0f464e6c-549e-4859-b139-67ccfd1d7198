import { FormData } from '../../types'

interface StudentIDTemplateProps {
  formData: FormData
}

export default function StudentIDTemplate({ formData }: StudentIDTemplateProps) {
  return (
    <div className="max-w-md mx-auto bg-white border-2 border-gray-300 rounded-lg overflow-hidden shadow-lg font-serif">
      {/* Header */}
      <div className="bg-academic-maroon text-white text-center py-3">
        <div className="flex items-center justify-center space-x-3">
          <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
            <span className="text-academic-maroon font-bold text-sm">ASU</span>
          </div>
          <div>
            <h1 className="text-lg font-bold">ARIZONA STATE UNIVERSITY</h1>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-4">
        <div className="flex items-start space-x-4">
          {/* Photo Placeholder */}
          <div className="w-20 h-24 bg-gray-200 border border-gray-300 flex items-center justify-center text-xs text-gray-500 flex-shrink-0">
            PHOTO
          </div>

          {/* Student Information */}
          <div className="flex-1 space-y-2">
            <div>
              <div className="text-xs font-semibold text-gray-600 uppercase">Name</div>
              <div className="text-sm font-bold">{formData.fullName || 'Student Name'}</div>
            </div>
            
            <div>
              <div className="text-xs font-semibold text-gray-600 uppercase">ASU ID</div>
              <div className="text-sm font-bold">{formData.studentId || '1234567890'}</div>
            </div>
            
            <div>
              <div className="text-xs font-semibold text-gray-600 uppercase">Major</div>
              <div className="text-xs">{formData.major || 'Computer Science'}</div>
            </div>
            
            <div>
              <div className="text-xs font-semibold text-gray-600 uppercase">School</div>
              <div className="text-xs">{formData.school || 'Ira A. Fulton Schools of Engineering'}</div>
            </div>
            
            <div>
              <div className="text-xs font-semibold text-gray-600 uppercase">Valid Through</div>
              <div className="text-xs font-bold">{formData.validThrough || '05/31/2029'}</div>
            </div>
          </div>

          {/* Barcode Placeholder */}
          <div className="w-16 h-8 bg-gray-200 border border-gray-300 flex items-center justify-center text-xs text-gray-500 flex-shrink-0">
            ||||||||
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-academic-gold text-academic-maroon text-center py-2 text-xs font-bold">
        ASU IDENTIFICATION CARD • PROPERTY OF ARIZONA STATE UNIVERSITY • NOT TRANSFERABLE
      </div>

      {/* Watermark */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-5">
        <div className="text-6xl font-bold text-gray-400 transform rotate-45">
          ASU
        </div>
      </div>
    </div>
  )
}

// Export HTML string version for download
export function StudentIDTemplateHTML(formData: FormData): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>ASU Student ID - ${formData.fullName}</title>
      <style>
        body { 
          font-family: 'Times New Roman', serif; 
          margin: 0; 
          padding: 20px; 
          background: #f5f5f5; 
          display: flex;
          justify-content: center;
          align-items: center;
          min-height: 100vh;
        }
        .id-card { 
          width: 350px;
          background: white; 
          border: 2px solid #ccc; 
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
          position: relative;
        }
        .header { 
          background: #A31F34; 
          color: white; 
          text-align: center; 
          padding: 12px; 
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
        }
        .logo { 
          width: 40px; 
          height: 40px; 
          background: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #A31F34;
          font-weight: bold;
          font-size: 12px;
        }
        .header h1 {
          font-size: 18px;
          font-weight: bold;
          margin: 0;
        }
        .content { 
          padding: 16px; 
          display: flex;
          gap: 16px;
        }
        .photo { 
          width: 80px; 
          height: 96px; 
          background: #e5e7eb; 
          border: 1px solid #ccc; 
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          color: #6b7280;
          flex-shrink: 0;
        }
        .info { 
          flex: 1; 
        }
        .field { 
          margin-bottom: 8px; 
        }
        .field-label { 
          font-size: 10px; 
          font-weight: bold; 
          color: #6b7280; 
          text-transform: uppercase; 
        }
        .field-value { 
          font-size: 12px; 
          font-weight: bold; 
        }
        .field-value.small { 
          font-size: 10px; 
          font-weight: normal; 
        }
        .barcode { 
          width: 64px; 
          height: 32px; 
          background: #e5e7eb; 
          border: 1px solid #ccc; 
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 10px;
          color: #6b7280;
          flex-shrink: 0;
        }
        .footer { 
          background: #FFC107; 
          color: #A31F34; 
          text-align: center; 
          padding: 8px; 
          font-size: 10px; 
          font-weight: bold; 
        }
        .watermark { 
          position: absolute; 
          top: 50%; 
          left: 50%; 
          transform: translate(-50%, -50%) rotate(45deg); 
          font-size: 60px; 
          font-weight: bold; 
          color: rgba(163, 31, 52, 0.05); 
          pointer-events: none; 
        }
        @media print {
          body { background: white; padding: 0; }
          .id-card { box-shadow: none; }
        }
      </style>
    </head>
    <body>
      <div class="id-card">
        <div class="header">
          <div class="logo">ASU</div>
          <h1>ARIZONA STATE UNIVERSITY</h1>
        </div>
        
        <div class="content">
          <div class="photo">PHOTO</div>
          
          <div class="info">
            <div class="field">
              <div class="field-label">Name</div>
              <div class="field-value">${formData.fullName || 'Student Name'}</div>
            </div>
            <div class="field">
              <div class="field-label">ASU ID</div>
              <div class="field-value">${formData.studentId || '1234567890'}</div>
            </div>
            <div class="field">
              <div class="field-label">Major</div>
              <div class="field-value small">${formData.major || 'Computer Science'}</div>
            </div>
            <div class="field">
              <div class="field-label">School</div>
              <div class="field-value small">${formData.school || 'Ira A. Fulton Schools of Engineering'}</div>
            </div>
            <div class="field">
              <div class="field-label">Valid Through</div>
              <div class="field-value">${formData.validThrough || '05/31/2029'}</div>
            </div>
          </div>
          
          <div class="barcode">||||||||</div>
        </div>
        
        <div class="footer">
          ASU IDENTIFICATION CARD • PROPERTY OF ARIZONA STATE UNIVERSITY • NOT TRANSFERABLE
        </div>
        
        <div class="watermark">ASU</div>
      </div>
    </body>
    </html>
  `
}
