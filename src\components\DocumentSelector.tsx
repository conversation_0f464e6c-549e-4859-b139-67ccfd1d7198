import { useState } from 'react'
import type { DocumentType } from '../types'
import { DOCUMENT_TEMPLATES } from '../types'

interface DocumentSelectorProps {
  onDocumentSelection: (documents: DocumentType[]) => void
}

export default function DocumentSelector({ onDocumentSelection }: DocumentSelectorProps) {
  const [selectedDocuments, setSelectedDocuments] = useState<DocumentType[]>([])

  const handleDocumentToggle = (documentId: DocumentType) => {
    setSelectedDocuments(prev => 
      prev.includes(documentId)
        ? prev.filter(id => id !== documentId)
        : [...prev, documentId]
    )
  }

  const handleContinue = () => {
    if (selectedDocuments.length > 0) {
      onDocumentSelection(selectedDocuments)
    }
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Select Document Types
        </h2>
        <p className="text-lg text-gray-600">
          Choose the academic documents you want to generate. You can select multiple documents.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {DOCUMENT_TEMPLATES.map((template) => (
          <div
            key={template.id}
            className={`card cursor-pointer transition-all duration-200 hover:shadow-lg border-2 ${
              selectedDocuments.includes(template.id)
                ? 'border-academic-maroon bg-red-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleDocumentToggle(template.id)}
          >
            <div className="flex items-start space-x-4">
              <div className="text-4xl">{template.icon}</div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {template.name}
                  </h3>
                  <input
                    type="checkbox"
                    checked={selectedDocuments.includes(template.id)}
                    onChange={() => handleDocumentToggle(template.id)}
                    className="w-5 h-5 text-academic-maroon bg-gray-100 border-gray-300 rounded focus:ring-academic-maroon focus:ring-2"
                  />
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  {template.description}
                </p>
                <div className="flex flex-wrap gap-1">
                  {template.fields.slice(0, 3).map((field) => (
                    <span
                      key={field}
                      className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                    >
                      {field.replace(/([A-Z])/g, ' $1').toLowerCase()}
                    </span>
                  ))}
                  {template.fields.length > 3 && (
                    <span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                      +{template.fields.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-center">
        <button
          onClick={handleContinue}
          disabled={selectedDocuments.length === 0}
          className={`px-8 py-3 rounded-lg font-medium text-lg transition-colors duration-200 ${
            selectedDocuments.length > 0
              ? 'btn-primary'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          Continue with {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''}
        </button>
      </div>

      {selectedDocuments.length > 0 && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Selected Documents:</h4>
          <ul className="list-disc list-inside text-blue-800">
            {selectedDocuments.map((docId) => {
              const template = DOCUMENT_TEMPLATES.find(t => t.id === docId)
              return (
                <li key={docId}>{template?.name}</li>
              )
            })}
          </ul>
        </div>
      )}
    </div>
  )
}
