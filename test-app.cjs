// Simple test script to verify the application is working
const http = require('http');

const testEndpoint = (url, description) => {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`✅ ${description}: SUCCESS (${res.statusCode})`);
          resolve(data);
        } else {
          console.log(`❌ ${description}: FAILED (${res.statusCode})`);
          reject(new Error(`HTTP ${res.statusCode}`));
        }
      });
    });
    
    req.on('error', (err) => {
      console.log(`❌ ${description}: ERROR - ${err.message}`);
      reject(err);
    });
    
    req.setTimeout(5000, () => {
      console.log(`❌ ${description}: TIMEOUT`);
      req.destroy();
      reject(new Error('Timeout'));
    });
  });
};

async function runTests() {
  console.log('🧪 Testing Academic Document Generator Application...\n');
  
  try {
    // Test main application
    const html = await testEndpoint('http://localhost:5173/', 'Main Application');
    
    // Check if the HTML contains expected content
    if (html.includes('Academic Document Generator')) {
      console.log('✅ Application title found in HTML');
    } else {
      console.log('❌ Application title not found in HTML');
    }
    
    if (html.includes('React')) {
      console.log('✅ React application detected');
    } else {
      console.log('❌ React application not detected');
    }
    
    console.log('\n🎉 Application is running successfully!');
    console.log('📝 Manual testing steps:');
    console.log('1. Open http://localhost:5173/ in your browser');
    console.log('2. Select one or more document types');
    console.log('3. Fill out the form with sample data');
    console.log('4. Preview the generated documents');
    console.log('5. Test the download and print functionality');
    
  } catch (error) {
    console.log('\n❌ Application test failed:', error.message);
    console.log('🔧 Make sure the development server is running with: npm run dev');
  }
}

runTests();
