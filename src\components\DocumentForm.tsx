import { useState, useEffect } from 'react'
import type { DocumentType, FormData } from '../types'
import { DOCUMENT_TEMPLATES } from '../types'
import FormField from './FormField'
import CourseEditor from './CourseEditor'

interface DocumentFormProps {
  selectedDocuments: DocumentType[]
  initialData: FormData
  onSubmit: (data: FormData) => void
  onBack: () => void
}

export default function DocumentForm({ 
  selectedDocuments, 
  initialData, 
  onSubmit, 
  onBack 
}: DocumentFormProps) {
  const [formData, setFormData] = useState<FormData>(initialData)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [currentSection, setCurrentSection] = useState(0)

  // Get all unique fields needed for selected documents
  const requiredFields = Array.from(new Set(
    selectedDocuments.flatMap(docId => 
      DOCUMENT_TEMPLATES.find(t => t.id === docId)?.fields || []
    )
  ))

  const sections = [
    {
      title: 'Personal Information',
      fields: ['fullName', 'studentId', 'major', 'school', 'gender', 'dateOfBirth', 'age', 'ethnicity', 'address']
    },
    {
      title: 'Academic Information',
      fields: ['campus', 'expectedStart', 'expectedCompletion', 'institutionalGpa', 'majorGpa', 'academicStanding', 'highSchoolName', 'graduationDate', 'highSchoolGpa']
    },
    {
      title: 'Document Details',
      fields: ['validThrough', 'issueDate', 'validUntil', 'verificationCode', 'verificationCodeTr', 'semester', 'academicYear', 'scheduleTerms']
    },
    {
      title: 'Financial Information',
      fields: ['tuitionAmount', 'feesAmount', 'totalAmount', 'paymentDate', 'paymentMethod']
    },
    {
      title: 'Academic Records',
      fields: ['totalCreditsAttempted', 'totalCreditsEarned', 'totalQualityPoints', 'priorSchool', 'priorSchoolCredits', 'priorSchoolDegree']
    }
  ]

  const currentSectionFields = sections[currentSection]?.fields.filter(field => 
    requiredFields.includes(field)
  ) || []

  const handleFieldChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateCurrentSection = () => {
    const newErrors: Record<string, string> = {}
    
    currentSectionFields.forEach(field => {
      if (!formData[field as keyof FormData] && requiredFields.includes(field)) {
        newErrors[field] = 'This field is required'
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateCurrentSection()) {
      if (currentSection < sections.length - 1) {
        setCurrentSection(prev => prev + 1)
      } else {
        onSubmit(formData)
      }
    }
  }

  const handlePrevious = () => {
    if (currentSection > 0) {
      setCurrentSection(prev => prev - 1)
    } else {
      onBack()
    }
  }

  const getFieldLabel = (field: string) => {
    return field.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
  }

  const getFieldType = (field: string) => {
    if (field.includes('Date') || field.includes('date')) return 'date'
    if (field.includes('Amount') || field.includes('amount')) return 'number'
    if (field.includes('Email') || field.includes('email')) return 'email'
    return 'text'
  }

  // Skip sections that have no required fields
  const availableSections = sections.filter(section => 
    section.fields.some(field => requiredFields.includes(field))
  )

  useEffect(() => {
    // Auto-advance to first section with required fields
    const firstSectionIndex = sections.findIndex(section => 
      section.fields.some(field => requiredFields.includes(field))
    )
    if (firstSectionIndex !== -1 && firstSectionIndex !== currentSection) {
      setCurrentSection(firstSectionIndex)
    }
  }, [selectedDocuments])

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Document Information
        </h2>
        <p className="text-lg text-gray-600 mb-6">
          Fill in the required information for your selected documents.
        </p>
        
        {/* Progress indicator */}
        <div className="flex items-center justify-between mb-6">
          {availableSections.map((section, index) => (
            <div key={section.title} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                index <= currentSection 
                  ? 'bg-academic-maroon text-white' 
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {index + 1}
              </div>
              <span className={`ml-2 text-sm ${
                index <= currentSection ? 'text-academic-maroon font-medium' : 'text-gray-500'
              }`}>
                {section.title}
              </span>
              {index < availableSections.length - 1 && (
                <div className={`w-12 h-0.5 mx-4 ${
                  index < currentSection ? 'bg-academic-maroon' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      <div className="card">
        <h3 className="text-xl font-semibold text-gray-900 mb-6">
          {availableSections[currentSection]?.title}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {currentSectionFields.map((field) => (
            <FormField
              key={field}
              label={getFieldLabel(field)}
              type={getFieldType(field)}
              value={formData[field as keyof FormData] as string}
              onChange={(value) => handleFieldChange(field, value)}
              error={errors[field]}
              required={requiredFields.includes(field)}
            />
          ))}
        </div>

        {/* Course editors for transcript documents */}
        {selectedDocuments.includes('universityTranscript') && currentSection === availableSections.length - 1 && (
          <div className="mt-8">
            <CourseEditor
              courses={formData.courses}
              onChange={(courses) => setFormData(prev => ({ ...prev, courses }))}
              title="University Courses"
            />
          </div>
        )}

        <div className="flex justify-between mt-8">
          <button
            onClick={handlePrevious}
            className="btn-secondary"
          >
            {currentSection === 0 ? 'Back to Selection' : 'Previous'}
          </button>
          
          <button
            onClick={handleNext}
            className="btn-primary"
          >
            {currentSection === availableSections.length - 1 ? 'Generate Documents' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  )
}
