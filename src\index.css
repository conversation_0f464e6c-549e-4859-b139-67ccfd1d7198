@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply text-gray-900 bg-gray-50;
    font-family: 'Inter', system-ui, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Times New Roman', serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-academic-maroon hover:bg-red-800 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-academic-maroon focus:border-transparent;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .document-template {
    @apply bg-white border border-gray-200 rounded-lg p-6 shadow-sm;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Print styles */
@media print {
  body {
    @apply bg-white;
  }

  .no-print {
    display: none !important;
  }

  .document-template {
    @apply shadow-none border-0 p-0;
    page-break-inside: avoid;
  }

  .page-break {
    page-break-before: always;
  }
}
