import type { FormData } from '../../types'

interface TuitionReceiptTemplateProps {
  formData: FormData
}

export default function TuitionReceiptTemplate({ formData }: TuitionReceiptTemplateProps) {
  const currentDate = new Date().toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  })

  const receiptNumber = `ASU-${Date.now()}`
  const tuition = parseFloat(formData.tuitionAmount || '12500')
  const fees = parseFloat(formData.feesAmount || '1250')
  const total = tuition + fees

  return (
    <div className="max-w-4xl mx-auto bg-white p-8 font-serif text-sm">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="bg-academic-maroon text-white p-6 rounded-lg">
          <div className="flex items-center justify-center space-x-4 mb-3">
            <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center">
              <span className="text-academic-maroon font-bold text-lg">ASU</span>
            </div>
            <div>
              <h1 className="text-3xl font-bold">ARIZONA STATE UNIVERSITY</h1>
              <p className="text-lg">Student Financial Services</p>
            </div>
          </div>
          <h2 className="text-xl font-bold bg-academic-gold text-academic-maroon py-2 px-4 rounded">
            TUITION & FEES RECEIPT
          </h2>
        </div>
      </div>

      {/* Receipt Information */}
      <div className="grid grid-cols-2 gap-8 mb-8">
        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-bold text-academic-maroon mb-3">RECEIPT INFORMATION</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-semibold">Receipt Number:</span>
                <span>{receiptNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Payment Date:</span>
                <span>{formData.paymentDate || currentDate}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Payment Method:</span>
                <span>{formData.paymentMethod || 'Online Payment'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Transaction ID:</span>
                <span>TXN-{Date.now().toString().slice(-8)}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-bold text-academic-maroon mb-3">STUDENT INFORMATION</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="font-semibold">Name:</span>
                <span>{formData.fullName || 'Student Name'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Student ID:</span>
                <span>{formData.studentId || '1234567890'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">Major:</span>
                <span>{formData.major || 'Computer Science'}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-semibold">School:</span>
                <span>{formData.school || 'Engineering'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Term Information */}
      <div className="mb-8">
        <h3 className="bg-academic-maroon text-white p-3 font-bold">TERM INFORMATION</h3>
        <div className="border border-gray-300 p-4 bg-gray-50">
          <div className="grid grid-cols-3 gap-8">
            <div>
              <span className="font-semibold">Academic Year:</span>
              <div className="text-lg">{formData.academicYear || '2024-2025'}</div>
            </div>
            <div>
              <span className="font-semibold">Semester:</span>
              <div className="text-lg">{formData.semester || 'Fall 2024'}</div>
            </div>
            <div>
              <span className="font-semibold">Enrollment Status:</span>
              <div className="text-lg">Full-Time</div>
            </div>
          </div>
        </div>
      </div>

      {/* Charges Breakdown */}
      <div className="mb-8">
        <h3 className="bg-academic-maroon text-white p-3 font-bold">CHARGES BREAKDOWN</h3>
        <table className="w-full border-collapse border border-gray-300">
          <thead>
            <tr className="bg-gray-100">
              <th className="border border-gray-300 p-3 text-left">Description</th>
              <th className="border border-gray-300 p-3 text-left">Details</th>
              <th className="border border-gray-300 p-3 text-right">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td className="border border-gray-300 p-3 font-semibold">Tuition</td>
              <td className="border border-gray-300 p-3">Resident Undergraduate - 15 Credit Hours</td>
              <td className="border border-gray-300 p-3 text-right">${tuition.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-3 font-semibold">Student Fees</td>
              <td className="border border-gray-300 p-3">Technology, Recreation, Health Services</td>
              <td className="border border-gray-300 p-3 text-right">${fees.toLocaleString('en-US', { minimumFractionDigits: 2 })}</td>
            </tr>
            <tr className="bg-gray-50">
              <td className="border border-gray-300 p-3 font-bold">TOTAL CHARGES</td>
              <td className="border border-gray-300 p-3"></td>
              <td className="border border-gray-300 p-3 text-right font-bold text-lg">
                ${total.toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Payment Information */}
      <div className="mb-8">
        <h3 className="bg-academic-maroon text-white p-3 font-bold">PAYMENT INFORMATION</h3>
        <table className="w-full border-collapse border border-gray-300">
          <tbody>
            <tr>
              <td className="border border-gray-300 p-3 font-semibold bg-gray-50 w-1/3">Payment Amount</td>
              <td className="border border-gray-300 p-3 text-right font-bold text-lg">
                ${total.toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-3 font-semibold bg-gray-50">Payment Method</td>
              <td className="border border-gray-300 p-3">{formData.paymentMethod || 'Credit Card (****1234)'}</td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-3 font-semibold bg-gray-50">Payment Status</td>
              <td className="border border-gray-300 p-3">
                <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-semibold">
                  PAID IN FULL
                </span>
              </td>
            </tr>
            <tr>
              <td className="border border-gray-300 p-3 font-semibold bg-gray-50">Balance Due</td>
              <td className="border border-gray-300 p-3 text-right font-bold">$0.00</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Important Information */}
      <div className="mb-8">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-bold text-blue-900 mb-2">📋 Important Information</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• This receipt serves as proof of payment for the specified term</li>
            <li>• Keep this receipt for your tax and financial records</li>
            <li>• Refund policies apply as outlined in the Student Financial Services handbook</li>
            <li>• For questions, contact Student Financial Services at (*************</li>
          </ul>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center text-xs text-gray-600 border-t pt-6">
        <div className="mb-4">
          <p className="font-bold">ARIZONA STATE UNIVERSITY - STUDENT FINANCIAL SERVICES</p>
          <p>PO Box 870412, Tempe, AZ 85287-0412</p>
          <p>Phone: (************* | Email: <EMAIL> | Web: students.asu.edu</p>
        </div>
        <div className="flex justify-between items-center">
          <span>Receipt Generated: {currentDate}</span>
          <span>Document ID: {receiptNumber}</span>
          <span>Page 1 of 1</span>
        </div>
      </div>

      {/* Watermark */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-5">
        <div className="text-9xl font-bold text-gray-400 transform rotate-45">
          PAID
        </div>
      </div>
    </div>
  )
}
